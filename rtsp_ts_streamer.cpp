#include <liveMedia.hh>
#include <BasicUsageEnvironment.hh>
#include <GroupsockHelper.hh>
#include <iostream>
#include <string>

class TSFileServerMediaSubsession : public OnDemandServerMediaSubsession {
public:
    static TSFileServerMediaSubsession* createNew(UsageEnvironment& env, 
                                                  char const* fileName);

protected:
    TSFileServerMediaSubsession(UsageEnvironment& env, char const* fileName);
    virtual ~TSFileServerMediaSubsession();

    virtual FramedSource* createNewStreamSource(unsigned clientSessionId,
                                              unsigned& estBitrate);
    virtual RTPSink* createNewRTPSink(Groupsock* rtpGroupsock,
                                    unsigned char rtpPayloadTypeIfDynamic,
                                    FramedSource* inputSource);

private:
    char* fFileName;
};

class RTSPTSStreamer {
private:
    TaskScheduler* scheduler;
    UsageEnvironment* env;
    RTSPServer* rtspServer;
    ServerMediaSession* sms;
    std::string currentTSFile;
    
public:
    RTSPTSStreamer(int port = 8554);
    ~RTSPTSStreamer();
    
    bool initialize();
    void setTSFile(const std::string& tsFilePath);
    void startStreaming();
    void stopStreaming();
    std::string getRTSPURL() const;
};

// Implementation
TSFileServerMediaSubsession* TSFileServerMediaSubsession::createNew(
    UsageEnvironment& env, char const* fileName) {
    return new TSFileServerMediaSubsession(env, fileName);
}

TSFileServerMediaSubsession::TSFileServerMediaSubsession(
    UsageEnvironment& env, char const* fileName)
    : OnDemandServerMediaSubsession(env, True) {
    fFileName = strDup(fileName);
}

TSFileServerMediaSubsession::~TSFileServerMediaSubsession() {
    delete[] fFileName;
}

FramedSource* TSFileServerMediaSubsession::createNewStreamSource(
    unsigned clientSessionId, unsigned& estBitrate) {
    estBitrate = 5000; // 5 Mbps
    
    envir() << "Creating stream source for client " << clientSessionId 
            << ", file: " << fFileName << "\n";
    
    ByteStreamFileSource* fileSource = ByteStreamFileSource::createNew(envir(), fFileName);
    if (fileSource == NULL) {
        envir() << "Failed to create ByteStreamFileSource for " << fFileName << "\n";
        return NULL;
    }
    
    // 直接返回文件源，不使用MPEG2TransportStreamFramer
    envir() << "Successfully created TS stream source (direct)\n";
    return fileSource;
}

RTPSink* TSFileServerMediaSubsession::createNewRTPSink(
    Groupsock* rtpGroupsock, unsigned char rtpPayloadTypeIfDynamic,
    FramedSource* inputSource) {
    
    envir() << "Creating RTP sink for TS stream\n";
    
    // 使用更合适的参数
    return SimpleRTPSink::createNew(envir(), rtpGroupsock, 
                                   33,        // payload type for MP2T
                                   90000,     // timestamp frequency
                                   "video",   // media type
                                   "MP2T",    // RTP format name
                                   1,         // number of channels
                                   True,      // allow multiple frames per packet
                                   True);     // marker bit on last fragment
}

RTSPTSStreamer::RTSPTSStreamer(int port) {
    scheduler = BasicTaskScheduler::createNew();
    env = BasicUsageEnvironment::createNew(*scheduler);
    rtspServer = RTSPServer::createNew(*env, port, NULL);
    sms = NULL;
}

RTSPTSStreamer::~RTSPTSStreamer() {
    stopStreaming();
    Medium::close(rtspServer);
    env->reclaim();
    delete scheduler;
}

bool RTSPTSStreamer::initialize() {
    if (rtspServer == NULL) {
        *env << "Failed to create RTSP server\n";
        return false;
    }
    return true;
}

void RTSPTSStreamer::setTSFile(const std::string& tsFilePath) {
    stopStreaming();
    currentTSFile = tsFilePath;
    
    sms = ServerMediaSession::createNew(*env, "tsStream", 
                                       "TS Stream", "Session streamed by live555");
    sms->addSubsession(TSFileServerMediaSubsession::createNew(*env, 
                                                             currentTSFile.c_str()));
    rtspServer->addServerMediaSession(sms);
    
    std::cout << "Set TS file: " << currentTSFile << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
}

void RTSPTSStreamer::startStreaming() {
    if (sms == NULL) {
        std::cout << "No TS file set. Call setTSFile() first." << std::endl;
        return;
    }
    
    std::cout << "Starting RTSP streaming..." << std::endl;
    env->taskScheduler().doEventLoop();
}

void RTSPTSStreamer::stopStreaming() {
    if (sms != NULL) {
        rtspServer->removeServerMediaSession(sms);
        sms = NULL;
    }
}

std::string RTSPTSStreamer::getRTSPURL() const {
    if (rtspServer == NULL) return "";
    
    char* url = rtspServer->rtspURL(sms);
    std::string result(url);
    delete[] url;
    return result;
}

int main() {
    RTSPTSStreamer streamer(8554);
    
    if (!streamer.initialize()) {
        std::cerr << "Failed to initialize RTSP streamer" << std::endl;
        return -1;
    }
    
    // 设置初始TS文件
    streamer.setTSFile("/home/<USER>/zeno/rtsp_ts/video.ts");
    
    // 在另一个线程中可以随时切换文件
    // streamer.setTSFile("./another_video.ts");
    
    streamer.startStreaming();
    
    while(1) 
    {
        sleep(1);
    }
    return 0;
}



