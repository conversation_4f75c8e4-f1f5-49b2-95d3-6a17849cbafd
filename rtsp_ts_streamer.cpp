#include <liveMedia.hh>
#include <BasicUsageEnvironment.hh>
#include <GroupsockHelper.hh>
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <vector>

#ifdef _WIN32
    #include <windows.h>
    #include <io.h>
    #define sleep(x) Sleep((x)*1000)
    #define access _access
    #define F_OK 0
#else
    #include <unistd.h>
    #include <signal.h>
#endif

class TSFileServerMediaSubsession : public OnDemandServerMediaSubsession {
public:
    static TSFileServerMediaSubsession* createNew(UsageEnvironment& env, 
                                                  char const* fileName);

protected:
    TSFileServerMediaSubsession(UsageEnvironment& env, char const* fileName);
    virtual ~TSFileServerMediaSubsession();

    virtual FramedSource* createNewStreamSource(unsigned clientSessionId,
                                              unsigned& estBitrate);
    virtual RTPSink* createNewRTPSink(Groupsock* rtpGroupsock,
                                    unsigned char rtpPayloadTypeIfDynamic,
                                    FramedSource* inputSource);

private:
    char* fFileName;
};

class RTSPTSStreamer {
private:
    TaskScheduler* scheduler;
    UsageEnvironment* env;
    RTSPServer* rtspServer;
    ServerMediaSession* sms;
    std::string currentTSFile;
    std::mutex fileMutex;
    std::atomic<bool> isRunning;
    std::atomic<bool> shouldStop;
    int serverPort;

public:
    RTSPTSStreamer(int port = 8554);
    ~RTSPTSStreamer();

    bool initialize();
    bool setTSFile(const std::string& tsFilePath);
    void startStreaming();
    void stopStreaming();
    std::string getRTSPURL() const;
    bool isFileExists(const std::string& filePath) const;
    void runEventLoop();
};

// Implementation
TSFileServerMediaSubsession* TSFileServerMediaSubsession::createNew(
    UsageEnvironment& env, char const* fileName) {
    return new TSFileServerMediaSubsession(env, fileName);
}

TSFileServerMediaSubsession::TSFileServerMediaSubsession(
    UsageEnvironment& env, char const* fileName)
    : OnDemandServerMediaSubsession(env, True) {
    fFileName = strDup(fileName);
}

TSFileServerMediaSubsession::~TSFileServerMediaSubsession() {
    delete[] fFileName;
}

FramedSource* TSFileServerMediaSubsession::createNewStreamSource(
    unsigned clientSessionId, unsigned& estBitrate) {
    estBitrate = 5000; // 5 Mbps

    envir() << "Creating stream source for client " << clientSessionId
            << ", file: " << fFileName << "\n";

    // 检查文件是否存在
    FILE* testFile = fopen(fFileName, "rb");
    if (testFile == NULL) {
        envir() << "Error: Cannot open file " << fFileName << "\n";
        return NULL;
    }
    fclose(testFile);

    ByteStreamFileSource* fileSource = ByteStreamFileSource::createNew(envir(), fFileName);
    if (fileSource == NULL) {
        envir() << "Failed to create ByteStreamFileSource for " << fFileName << "\n";
        return NULL;
    }

    // 使用MPEG2TransportStreamFramer来正确处理TS流
    MPEG2TransportStreamFramer* framer =
        MPEG2TransportStreamFramer::createNew(envir(), fileSource);
    if (framer == NULL) {
        envir() << "Failed to create MPEG2TransportStreamFramer\n";
        Medium::close(fileSource);
        return NULL;
    }

    envir() << "Successfully created TS stream source with framer\n";
    return framer;
}

RTPSink* TSFileServerMediaSubsession::createNewRTPSink(
    Groupsock* rtpGroupsock, unsigned char rtpPayloadTypeIfDynamic,
    FramedSource* inputSource) {
    
    envir() << "Creating RTP sink for TS stream\n";
    
    // 使用更合适的参数
    return SimpleRTPSink::createNew(envir(), rtpGroupsock, 
                                   33,        // payload type for MP2T
                                   90000,     // timestamp frequency
                                   "video",   // media type
                                   "MP2T",    // RTP format name
                                   1,         // number of channels
                                   True,      // allow multiple frames per packet
                                   True);     // marker bit on last fragment
}

RTSPTSStreamer::RTSPTSStreamer(int port) : serverPort(port), isRunning(false), shouldStop(false) {
    scheduler = BasicTaskScheduler::createNew();
    env = BasicUsageEnvironment::createNew(*scheduler);
    rtspServer = RTSPServer::createNew(*env, port, NULL);
    sms = NULL;
}

RTSPTSStreamer::~RTSPTSStreamer() {
    stopStreaming();
    Medium::close(rtspServer);
    env->reclaim();
    delete scheduler;
}

bool RTSPTSStreamer::initialize() {
    if (rtspServer == NULL) {
        *env << "Failed to create RTSP server\n";
        return false;
    }
    return true;
}

bool RTSPTSStreamer::setTSFile(const std::string& tsFilePath) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 检查文件是否存在
    if (!isFileExists(tsFilePath)) {
        std::cerr << "Error: TS file does not exist: " << tsFilePath << std::endl;
        return false;
    }

    stopStreaming();
    currentTSFile = tsFilePath;

    sms = ServerMediaSession::createNew(*env, "tsStream",
                                       "TS Stream", "Session streamed by live555");
    sms->addSubsession(TSFileServerMediaSubsession::createNew(*env,
                                                             currentTSFile.c_str()));
    rtspServer->addServerMediaSession(sms);

    std::cout << "Set TS file: " << currentTSFile << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
    return true;
}

void RTSPTSStreamer::startStreaming() {
    if (sms == NULL) {
        std::cout << "No TS file set. Call setTSFile() first." << std::endl;
        return;
    }

    isRunning = true;
    shouldStop = false;
    std::cout << "Starting RTSP streaming..." << std::endl;
    std::cout << "Server listening on port " << serverPort << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
}

void RTSPTSStreamer::runEventLoop() {
    if (!isRunning) {
        std::cout << "Streamer not started. Call startStreaming() first." << std::endl;
        return;
    }

    // 运行事件循环，但允许中断
    env->taskScheduler().doEventLoop(&shouldStop);
}

void RTSPTSStreamer::stopStreaming() {
    shouldStop = true;
    isRunning = false;

    if (sms != NULL) {
        rtspServer->removeServerMediaSession(sms);
        sms = NULL;
    }
}

std::string RTSPTSStreamer::getRTSPURL() const {
    if (rtspServer == NULL || sms == NULL) return "";

    char* url = rtspServer->rtspURL(sms);
    if (url == NULL) return "";

    std::string result(url);
    delete[] url;
    return result;
}

bool RTSPTSStreamer::isFileExists(const std::string& filePath) const {
#ifdef _WIN32
    return (_access(filePath.c_str(), F_OK) == 0);
#else
    return (access(filePath.c_str(), F_OK) == 0);
#endif
}

// 全局变量用于信号处理
RTSPTSStreamer* g_streamer = nullptr;

#ifdef _WIN32
BOOL WINAPI consoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\nReceived shutdown signal, stopping streamer..." << std::endl;
        if (g_streamer) {
            g_streamer->stopStreaming();
        }
        return TRUE;
    }
    return FALSE;
}
#else
void signalHandler(int signum) {
    std::cout << "\nReceived signal " << signum << ", shutting down..." << std::endl;
    if (g_streamer) {
        g_streamer->stopStreaming();
    }
    exit(signum);
}
#endif

// 业务逻辑：根据需要切换TS文件
void businessLogic(RTSPTSStreamer& streamer) {
    // 示例：每30秒切换一次文件（根据实际业务需求修改）
    std::vector<std::string> tsFiles = {
        "/home/<USER>/zeno/rtsp_ts/video.ts",
        "/home/<USER>/zeno/rtsp_ts/video2.ts",  // 如果有第二个文件
        // 可以添加更多文件路径
    };

    int currentIndex = 0;
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(30));

        // 根据业务逻辑选择下一个文件
        currentIndex = (currentIndex + 1) % tsFiles.size();

        std::cout << "Switching to file: " << tsFiles[currentIndex] << std::endl;
        if (!streamer.setTSFile(tsFiles[currentIndex])) {
            std::cerr << "Failed to switch to file: " << tsFiles[currentIndex] << std::endl;
        }
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
#ifdef _WIN32
    if (!SetConsoleCtrlHandler(consoleHandler, TRUE)) {
        std::cerr << "Warning: Could not set control handler" << std::endl;
    }
#else
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#endif

    // 创建RTSP流推送器
    RTSPTSStreamer streamer(8554);
    g_streamer = &streamer;

    if (!streamer.initialize()) {
        std::cerr << "Failed to initialize RTSP streamer" << std::endl;
        return -1;
    }

    // 设置初始TS文件
    std::string initialTSFile = "/home/<USER>/zeno/rtsp_ts/video.ts";

    // 如果命令行提供了文件路径，使用命令行参数
    if (argc > 1) {
        initialTSFile = argv[1];
    }

    if (!streamer.setTSFile(initialTSFile)) {
        std::cerr << "Failed to set initial TS file: " << initialTSFile << std::endl;
        return -1;
    }

    // 启动流推送
    streamer.startStreaming();

    // 在单独的线程中运行业务逻辑（文件切换逻辑）
    std::thread businessThread(businessLogic, std::ref(streamer));
    businessThread.detach();

    std::cout << "RTSP TS Streamer started successfully!" << std::endl;
    std::cout << "Press Ctrl+C to stop..." << std::endl;

    // 运行事件循环
    streamer.runEventLoop();

    return 0;
}



