/*
 * 业务逻辑示例 - 展示如何根据不同需求修改文件切换策略
 * 
 * 将这些函数替换到 rtsp_ts_streamer.cpp 中的 businessLogic 函数
 */

#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <ctime>

// 示例1: 定时轮播多个TS文件
void businessLogic_TimedRotation(RTSPTSStreamer& streamer) {
    std::vector<std::string> tsFiles = {
        "/home/<USER>/zeno/rtsp_ts/video1.ts",
        "/home/<USER>/zeno/rtsp_ts/video2.ts",
        "/home/<USER>/zeno/rtsp_ts/video3.ts",
    };
    
    int currentIndex = 0;
    while (true) {
        // 每30秒切换一次文件
        std::this_thread::sleep_for(std::chrono::seconds(30));
        
        currentIndex = (currentIndex + 1) % tsFiles.size();
        std::cout << "Switching to file: " << tsFiles[currentIndex] << std::endl;
        
        if (!streamer.setTSFile(tsFiles[currentIndex])) {
            std::cerr << "Failed to switch to file: " << tsFiles[currentIndex] << std::endl;
        }
    }
}

// 示例2: 基于时间段的文件切换（如早中晚不同内容）
void businessLogic_TimeBasedSwitching(RTSPTSStreamer& streamer) {
    struct TimeSlot {
        int hour;
        std::string filePath;
        std::string description;
    };
    
    std::vector<TimeSlot> schedule = {
        {6,  "/home/<USER>/zeno/rtsp_ts/morning_news.ts", "Morning News"},
        {12, "/home/<USER>/zeno/rtsp_ts/lunch_program.ts", "Lunch Program"},
        {18, "/home/<USER>/zeno/rtsp_ts/evening_show.ts", "Evening Show"},
        {22, "/home/<USER>/zeno/rtsp_ts/night_movie.ts", "Night Movie"},
    };
    
    std::string currentFile = "";
    
    while (true) {
        time_t now = time(0);
        tm* ltm = localtime(&now);
        int currentHour = ltm->tm_hour;
        
        // 找到当前时间应该播放的文件
        std::string targetFile = "";
        std::string description = "";
        
        for (int i = schedule.size() - 1; i >= 0; i--) {
            if (currentHour >= schedule[i].hour) {
                targetFile = schedule[i].filePath;
                description = schedule[i].description;
                break;
            }
        }
        
        // 如果没有找到，使用最后一个时间段的文件
        if (targetFile.empty()) {
            targetFile = schedule.back().filePath;
            description = schedule.back().description;
        }
        
        // 如果需要切换文件
        if (targetFile != currentFile) {
            std::cout << "Time-based switching to: " << description 
                      << " (" << targetFile << ")" << std::endl;
            
            if (streamer.setTSFile(targetFile)) {
                currentFile = targetFile;
            } else {
                std::cerr << "Failed to switch to: " << targetFile << std::endl;
            }
        }
        
        // 每分钟检查一次
        std::this_thread::sleep_for(std::chrono::minutes(1));
    }
}

// 示例3: 基于外部信号文件的切换
void businessLogic_SignalFileBased(RTSPTSStreamer& streamer) {
    std::string signalFile = "/tmp/rtsp_switch_signal.txt";
    std::string currentFile = "";
    
    while (true) {
        // 检查信号文件是否存在
        FILE* file = fopen(signalFile.c_str(), "r");
        if (file) {
            char buffer[1024];
            if (fgets(buffer, sizeof(buffer), file)) {
                std::string newFile(buffer);
                // 移除换行符
                if (!newFile.empty() && newFile.back() == '\n') {
                    newFile.pop_back();
                }
                
                if (newFile != currentFile && !newFile.empty()) {
                    std::cout << "Signal file triggered switch to: " << newFile << std::endl;
                    
                    if (streamer.setTSFile(newFile)) {
                        currentFile = newFile;
                        // 删除信号文件，表示已处理
                        remove(signalFile.c_str());
                    } else {
                        std::cerr << "Failed to switch to: " << newFile << std::endl;
                    }
                }
            }
            fclose(file);
        }
        
        // 每5秒检查一次信号文件
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

// 示例4: 基于文件大小或修改时间的自动切换
void businessLogic_FileMonitoring(RTSPTSStreamer& streamer) {
    std::string watchDir = "/home/<USER>/zeno/rtsp_ts/";
    std::string currentFile = "";
    time_t lastModTime = 0;
    
    while (true) {
        // 这里可以实现文件监控逻辑
        // 例如：监控目录中最新的TS文件，自动切换到最新文件
        
        // 简化示例：检查特定文件的修改时间
        std::string latestFile = watchDir + "latest.ts";
        
        struct stat fileStat;
        if (stat(latestFile.c_str(), &fileStat) == 0) {
            if (fileStat.st_mtime > lastModTime) {
                std::cout << "Detected new file: " << latestFile << std::endl;
                
                if (streamer.setTSFile(latestFile)) {
                    currentFile = latestFile;
                    lastModTime = fileStat.st_mtime;
                } else {
                    std::cerr << "Failed to switch to: " << latestFile << std::endl;
                }
            }
        }
        
        // 每10秒检查一次
        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
}

/*
 * 使用方法：
 * 
 * 1. 选择合适的业务逻辑函数
 * 2. 在 rtsp_ts_streamer.cpp 中替换 businessLogic 函数
 * 3. 根据实际需求修改文件路径和时间间隔
 * 4. 重新编译程序
 * 
 * 例如，要使用时间基础的切换：
 * 
 * std::thread businessThread(businessLogic_TimeBasedSwitching, std::ref(streamer));
 */
