LIVE555_PATH = /home/<USER>/3rdparty/bin/gcc-live555/usr/local
INCLUDES = -I$(LIVE555_PATH)/include/liveMedia \
           -I$(LIVE555_PATH)/include/groupsock \
           -I$(LIVE555_PATH)/include/UsageEnvironment \
           -I$(LIVE555_PATH)/include/BasicUsageEnvironment

LIBS = -L$(LIVE555_PATH)/lib \
       -lliveMedia \
       -lgroupsock \
       -lBasicUsageEnvironment \
       -lUsageEnvironment

CXX = g++
CXXFLAGS = -std=c++11 -O2 $(INCLUDES)

TARGET_SIMPLE = simple_rtsp_streamer
TARGET_FULL = rtsp_ts_streamer

all: $(TARGET_FULL)

simple: $(TARGET_SIMPLE)

$(TARGET_SIMPLE): simple_rtsp_streamer.cpp
	$(CXX) $(CXXFLAGS) -o $(TARGET_SIMPLE) simple_rtsp_streamer.cpp $(LIBS)

$(TARGET_FULL): rtsp_ts_streamer.cpp
	$(CXX) $(CXXFLAGS) -o $(TARGET_FULL) rtsp_ts_streamer.cpp $(LIBS)

clean:
	rm -f $(TARGET_SIMPLE) $(TARGET_FULL)

.PHONY: clean all simple
