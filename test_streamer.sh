#!/bin/bash

# 测试RTSP TS流推送器的脚本

echo "=== RTSP TS Streamer Test Script ==="

# 检查是否存在测试TS文件
if [ ! -f "video.ts" ]; then
    echo "Warning: video.ts not found in current directory"
    echo "Please ensure you have a valid TS file for testing"
fi

# 编译程序
echo "Building RTSP TS Streamer..."
make clean
make

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

# 运行程序
echo "Starting RTSP TS Streamer..."
echo "You can test the stream with:"
echo "  ffplay rtsp://localhost:8554/tsStream"
echo "  or"
echo "  vlc rtsp://localhost:8554/tsStream"
echo ""
echo "Press Ctrl+C to stop the streamer"

./rtsp_ts_streamer
