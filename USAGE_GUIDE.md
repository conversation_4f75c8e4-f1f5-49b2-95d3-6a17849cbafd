# RTSP TS Streamer 使用指南

## 项目概述

本项目提供了一个基于live555库的RTSP TS文件流推送器，支持通过RTSP协议推送TS文件，并且可以根据业务逻辑动态切换推送的TS文件。

## 核心特性

1. **RTSP流推送**: 通过标准RTSP协议推送TS文件流
2. **动态文件切换**: 运行时可以无缝切换不同的TS文件
3. **业务逻辑可定制**: 支持多种文件切换策略
4. **跨平台支持**: 支持Linux和Windows系统
5. **线程安全**: 多线程环境下安全的文件切换

## 文件结构

```
rtsp_ts/
├── rtsp_ts_streamer.cpp      # 完整版本（支持动态切换）
├── simple_rtsp_streamer.cpp  # 简化版本（基本功能）
├── business_logic_examples.cpp # 业务逻辑示例
├── Makefile                  # Linux编译文件
├── Makefile.simple          # 包含两个版本的编译选项
├── build.bat                # Windows编译脚本
├── test_streamer.sh         # Linux测试脚本
├── README.md                # 详细说明文档
├── USAGE_GUIDE.md           # 本使用指南
└── video.ts                 # 示例TS文件
```

## 快速开始

### 1. 环境准备

确保已安装live555库：
- 路径: `/home/<USER>/3rdparty/bin/gcc-live555/usr/local/`
- 包含头文件和库文件

### 2. 编译程序

**Linux系统:**
```bash
# 编译完整版本
make

# 编译简化版本
make -f Makefile.simple simple
```

**Windows系统:**
```bash
# 使用批处理文件
build.bat
```

### 3. 运行程序

**简化版本（推荐初次使用）:**
```bash
./simple_rtsp_streamer video.ts
```

**完整版本:**
```bash
./rtsp_ts_streamer video.ts
```

### 4. 测试流

使用以下工具测试RTSP流：

```bash
# 使用ffplay播放
ffplay rtsp://localhost:8554/tsStream

# 使用VLC播放
vlc rtsp://localhost:8554/tsStream

# 使用ffmpeg录制
ffmpeg -i rtsp://localhost:8554/tsStream -c copy output.ts
```

## 业务逻辑定制

### 内置策略

完整版本包含以下文件切换策略：

1. **定时轮播**: 每30秒自动切换到下一个文件
2. **时间段切换**: 根据时间段播放不同内容
3. **信号文件**: 通过外部文件控制切换
4. **文件监控**: 监控文件变化自动切换

### 自定义策略

参考 `business_logic_examples.cpp` 中的示例：

```cpp
void businessLogic_Custom(RTSPTSStreamer& streamer) {
    // 你的自定义逻辑
    while (true) {
        // 根据业务需求决定何时切换文件
        std::string newFile = determineNextFile();
        streamer.setTSFile(newFile);
        
        // 等待下次切换
        std::this_thread::sleep_for(std::chrono::seconds(60));
    }
}
```

## 常见使用场景

### 1. 固定文件循环播放

适用于广告轮播、宣传片循环等场景：

```cpp
std::vector<std::string> playlist = {
    "ad1.ts", "ad2.ts", "ad3.ts"
};
// 每个文件播放30秒后切换
```

### 2. 时间段节目切换

适用于电视台、网络直播等场景：

```cpp
// 6点新闻，12点午间节目，18点晚间节目
schedule = {
    {6, "morning_news.ts"},
    {12, "lunch_program.ts"}, 
    {18, "evening_show.ts"}
};
```

### 3. 实时内容更新

适用于监控录像回放、实时生成内容等场景：

```cpp
// 监控最新生成的TS文件，自动切换播放
watchDirectory("/path/to/recordings/");
```

## 性能优化建议

1. **文件预加载**: 提前检查文件可用性
2. **缓存策略**: 对频繁切换的文件进行缓存
3. **网络优化**: 调整RTP包大小和发送频率
4. **资源管理**: 及时释放不用的媒体会话

## 故障排除

### 编译问题
- 检查live555库路径是否正确
- 确认编译器支持C++11
- 验证所有依赖库是否安装

### 运行问题
- 确认TS文件存在且格式正确
- 检查端口8554是否被占用
- 验证防火墙设置

### 播放问题
- 使用多个客户端测试
- 检查网络连接
- 验证TS文件编码格式

## API参考

### RTSPTSStreamer类

主要方法：
- `initialize()`: 初始化服务器
- `setTSFile(path)`: 设置TS文件
- `startStreaming()`: 开始推流
- `stopStreaming()`: 停止推流
- `getRTSPURL()`: 获取RTSP地址

### 线程安全

所有公共方法都是线程安全的，可以在多线程环境中安全调用。

## 扩展开发

### 添加新的媒体格式

可以扩展支持其他媒体格式（如MP4、AVI等）：

```cpp
// 创建新的MediaSubsession子类
class MP4FileServerMediaSubsession : public OnDemandServerMediaSubsession {
    // 实现MP4文件的流处理
};
```

### 添加认证功能

可以添加RTSP认证：

```cpp
// 在创建RTSPServer时添加认证
UserAuthenticationDatabase* authDB = new UserAuthenticationDatabase;
authDB->addUserRecord("user", "password");
RTSPServer* rtspServer = RTSPServer::createNew(*env, port, authDB);
```

## 技术支持

如有问题，请检查：
1. live555库版本兼容性
2. 系统环境配置
3. 网络防火墙设置
4. TS文件格式规范
