# RTSP TS Streamer

基于live555库实现的RTSP TS文件流推送器，支持动态切换TS文件。

## 功能特性

- 通过RTSP协议推送TS文件流
- 支持动态切换TS文件（无需重启服务）
- 线程安全的文件切换机制
- 完善的错误处理和日志输出
- 支持信号处理（优雅关闭）
- 可配置的业务逻辑（文件切换策略）
- 跨平台支持（Linux/Windows）

## 依赖要求

- live555库（路径：/home/<USER>/3rdparty/bin/gcc-live555/usr/local/）
- C++11编译器
- Linux系统或Windows系统（带MinGW/MSYS2）

## 文件说明

- `rtsp_ts_streamer.cpp` - 完整版本，支持动态文件切换和业务逻辑
- `simple_rtsp_streamer.cpp` - 简化版本，适合基本使用和测试
- `Makefile` - 完整版本的编译文件
- `Makefile.simple` - 包含两个版本的编译选项

## 编译

### Linux系统

```bash
# 编译完整版本
make

# 编译简化版本
make -f Makefile.simple simple
```

### Windows系统

```bash
# 使用提供的批处理文件编译
build.bat

# 或者手动编译
g++ -std=c++11 -O2 -I/path/to/live555/include/... -o rtsp_ts_streamer.exe rtsp_ts_streamer.cpp -L/path/to/live555/lib -lliveMedia -lgroupsock -lBasicUsageEnvironment -lUsageEnvironment
```

## 使用方法

### 完整版本

```bash
# 使用默认TS文件
./rtsp_ts_streamer

# 指定TS文件路径
./rtsp_ts_streamer /path/to/your/video.ts
```

### 简化版本

```bash
# 使用默认TS文件
./simple_rtsp_streamer

# 指定TS文件路径
./simple_rtsp_streamer /path/to/your/video.ts
```

### 测试脚本

```bash
chmod +x test_streamer.sh
./test_streamer.sh
```

## RTSP URL

默认RTSP URL: `rtsp://localhost:8554/tsStream`

## 客户端测试

使用以下工具测试RTSP流：

```bash
# 使用ffplay
ffplay rtsp://localhost:8554/tsStream

# 使用VLC
vlc rtsp://localhost:8554/tsStream

# 使用ffmpeg保存流
ffmpeg -i rtsp://localhost:8554/tsStream -c copy output.ts
```

## 业务逻辑定制

在`businessLogic`函数中可以根据实际需求修改文件切换逻辑：

```cpp
void businessLogic(RTSPTSStreamer& streamer) {
    // 示例：每30秒切换一次文件
    std::vector<std::string> tsFiles = {
        "/path/to/video1.ts",
        "/path/to/video2.ts",
        // 添加更多文件...
    };
    
    int currentIndex = 0;
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(30));
        
        // 根据业务逻辑选择下一个文件
        currentIndex = (currentIndex + 1) % tsFiles.size();
        
        if (!streamer.setTSFile(tsFiles[currentIndex])) {
            std::cerr << "Failed to switch file" << std::endl;
        }
    }
}
```

## API说明

### RTSPTSStreamer类

- `RTSPTSStreamer(int port = 8554)`: 构造函数，指定RTSP服务端口
- `bool initialize()`: 初始化RTSP服务器
- `bool setTSFile(const std::string& tsFilePath)`: 设置要推送的TS文件
- `void startStreaming()`: 开始流推送
- `void runEventLoop()`: 运行事件循环
- `void stopStreaming()`: 停止流推送
- `std::string getRTSPURL() const`: 获取RTSP URL

## 注意事项

1. 确保TS文件存在且可读
2. 确保端口8554未被占用
3. 文件切换时会短暂中断流，这是正常现象
4. 使用Ctrl+C优雅关闭程序

## 故障排除

1. **编译错误**: 检查live555库路径是否正确
2. **端口占用**: 修改端口号或关闭占用端口的程序
3. **文件不存在**: 确保TS文件路径正确且文件存在
4. **流无法播放**: 检查TS文件格式是否正确
