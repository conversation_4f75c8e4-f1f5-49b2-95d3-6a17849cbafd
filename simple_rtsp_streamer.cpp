#include <liveMedia.hh>
#include <BasicUsageEnvironment.hh>
#include <GroupsockHelper.hh>
#include <iostream>
#include <string>

class TSFileServerMediaSubsession : public OnDemandServerMediaSubsession {
public:
    static TSFileServerMediaSubsession* createNew(UsageEnvironment& env, 
                                                  char const* fileName);

protected:
    TSFileServerMediaSubsession(UsageEnvironment& env, char const* fileName);
    virtual ~TSFileServerMediaSubsession();

    virtual FramedSource* createNewStreamSource(unsigned clientSessionId,
                                              unsigned& estBitrate);
    virtual RTPSink* createNewRTPSink(Groupsock* rtpGroupsock,
                                    unsigned char rtpPayloadTypeIfDynamic,
                                    FramedSource* inputSource);

private:
    char* fFileName;
};

// Implementation
TSFileServerMediaSubsession* TSFileServerMediaSubsession::createNew(
    UsageEnvironment& env, char const* fileName) {
    return new TSFileServerMediaSubsession(env, fileName);
}

TSFileServerMediaSubsession::TSFileServerMediaSubsession(
    UsageEnvironment& env, char const* fileName)
    : OnDemandServerMediaSubsession(env, True) {
    fFileName = strDup(fileName);
}

TSFileServerMediaSubsession::~TSFileServerMediaSubsession() {
    delete[] fFileName;
}

FramedSource* TSFileServerMediaSubsession::createNewStreamSource(
    unsigned clientSessionId, unsigned& estBitrate) {
    estBitrate = 5000; // 5 Mbps
    
    envir() << "Creating stream source for client " << clientSessionId 
            << ", file: " << fFileName << "\n";
    
    ByteStreamFileSource* fileSource = ByteStreamFileSource::createNew(envir(), fFileName);
    if (fileSource == NULL) {
        envir() << "Failed to create ByteStreamFileSource for " << fFileName << "\n";
        return NULL;
    }
    
    // 使用MPEG2TransportStreamFramer来正确处理TS流
    MPEG2TransportStreamFramer* framer = 
        MPEG2TransportStreamFramer::createNew(envir(), fileSource);
    if (framer == NULL) {
        envir() << "Failed to create MPEG2TransportStreamFramer\n";
        Medium::close(fileSource);
        return NULL;
    }
    
    envir() << "Successfully created TS stream source with framer\n";
    return framer;
}

RTPSink* TSFileServerMediaSubsession::createNewRTPSink(
    Groupsock* rtpGroupsock, unsigned char rtpPayloadTypeIfDynamic,
    FramedSource* inputSource) {
    
    envir() << "Creating RTP sink for TS stream\n";
    
    return SimpleRTPSink::createNew(envir(), rtpGroupsock, 
                                   33,        // payload type for MP2T
                                   90000,     // timestamp frequency
                                   "video",   // media type
                                   "MP2T",    // RTP format name
                                   1,         // number of channels
                                   True,      // allow multiple frames per packet
                                   True);     // marker bit on last fragment
}

int main(int argc, char* argv[]) {
    // 获取TS文件路径
    std::string tsFilePath = "video.ts";  // 默认文件
    if (argc > 1) {
        tsFilePath = argv[1];
    }
    
    std::cout << "Starting RTSP TS Streamer..." << std::endl;
    std::cout << "TS file: " << tsFilePath << std::endl;
    
    // 创建任务调度器和使用环境
    TaskScheduler* scheduler = BasicTaskScheduler::createNew();
    UsageEnvironment* env = BasicUsageEnvironment::createNew(*scheduler);
    
    // 创建RTSP服务器
    RTSPServer* rtspServer = RTSPServer::createNew(*env, 8554, NULL);
    if (rtspServer == NULL) {
        *env << "Failed to create RTSP server: " << env->getResultMsg() << "\n";
        exit(1);
    }
    
    // 创建服务器媒体会话
    ServerMediaSession* sms = ServerMediaSession::createNew(*env, "tsStream", 
                                                           "TS Stream", 
                                                           "Session streamed by live555");
    
    // 添加子会话
    sms->addSubsession(TSFileServerMediaSubsession::createNew(*env, tsFilePath.c_str()));
    
    // 将媒体会话添加到RTSP服务器
    rtspServer->addServerMediaSession(sms);
    
    // 获取并显示RTSP URL
    char* url = rtspServer->rtspURL(sms);
    std::cout << "RTSP URL: " << url << std::endl;
    std::cout << "Server started on port 8554" << std::endl;
    std::cout << "Press Ctrl+C to stop..." << std::endl;
    delete[] url;
    
    // 运行事件循环
    env->taskScheduler().doEventLoop(); // does not return
    
    return 0; // only to prevent compiler warning
}
