@echo off
echo Building RTSP TS Streamer...

REM 设置live555路径
set LIVE555_PATH=/home/<USER>/3rdparty/bin/gcc-live555/usr/local

REM 设置包含路径
set INCLUDES=-I%LIVE555_PATH%/include/liveMedia -I%LIVE555_PATH%/include/groupsock -I%LIVE555_PATH%/include/UsageEnvironment -I%LIVE555_PATH%/include/BasicUsageEnvironment

REM 设置库路径
set LIBS=-L%LIVE555_PATH%/lib -lliveMedia -lgroupsock -lBasicUsageEnvironment -lUsageEnvironment

REM 编译
g++ -std=c++11 -O2 %INCLUDES% -o rtsp_ts_streamer.exe rtsp_ts_streamer.cpp %LIBS%

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
) else (
    echo Build failed!
)

pause
